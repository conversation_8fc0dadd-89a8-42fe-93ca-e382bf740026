import React, { useState, useEffect, useRef } from 'react';

const Terminal = () => {
  const [input, setInput] = useState('');
  const [history, setHistory] = useState([
    { type: 'output', content: 'BAUHAUS TERMINAL v1.0', className: 'bauhaus-welcome' },
    { type: 'output', content: 'TYPE "HELP" FOR AVAILABLE COMMANDS', className: 'bauhaus-info' },
    { type: 'prompt', content: '', className: '' }
  ]);
  const [currentPath] = useState('~/BAUHAUS');
  const inputRef = useRef(null);
  const terminalRef = useRef(null);

  // Bauhaus command system
  const commands = {
    help: {
      output: [
        'AVAILABLE COMMANDS:',
        '├─ HELP     → SHOW THIS MESSAGE',
        '├─ CLEAR    → CLEAR TERMINAL',
        '├─ ABOUT    → SYSTEM INFORMATION', 
        '├─ SKILLS   → DEVELOPMENT SKILLS',
        '├─ PROJECTS → VIEW PROJECTS',
        '├─ CONTACT  → CONTACT INFORMATION',
        '└─ DATE     → CURRENT DATE'
      ],
      className: 'bauhaus-help'
    },
    clear: {
      output: [],
      action: 'clear'
    },
    about: {
      output: [
        'BAUHAUS DEV SYSTEM',
        '━━━━━━━━━━━━━━━━━━━',
        'DEVELOPER: ALEXANDRE',
        'DESIGN: BAUHAUS PRINCIPLES',
        'STACK: REACT + MODERN WEB',
        'STATUS: ACTIVE & BUILDING'
      ],
      className: 'bauhaus-about'
    },
    skills: {
      output: [
        'TECHNICAL SKILLS:',
        '█ FRONTEND → REACT, JAVASCRIPT, TYPESCRIPT',
        '█ STYLING → TAILWIND, CSS3, FRAMER MOTION', 
        '█ BACKEND → NODE.JS, EXPRESS, PYTHON',
        '█ TOOLS   → GIT, DOCKER, VS CODE',
        '█ DESIGN  → FIGMA, UI/UX, SYSTEMS'
      ],
      className: 'bauhaus-skills'
    },
    projects: {
      output: [
        'CURRENT PROJECTS:',
        '◆ PORTFOLIO WEBSITE',
        '◆ FORCE GRAPH VISUALIZATION',
        '◆ BAUHAUS TERMINAL INTERFACE',
        '◆ INTERACTIVE UI COMPONENTS',
        '◆ AI-POWERED DEV TOOLS'
      ],
      className: 'bauhaus-projects'
    },
    contact: {
      output: [
        'CONTACT INFORMATION:',
        '▲ EMAIL: <EMAIL>',
        '▲ GITHUB: GITHUB.COM/BAUHAUS-DEV',
        '▲ LINKEDIN: LINKEDIN.COM/IN/BAUHAUS',
        '▲ PORTFOLIO: BAUHAUS-DEV.COM'
      ],
      className: 'bauhaus-contact'
    },
    date: {
      output: [new Date().toLocaleString().toUpperCase()],
      className: 'bauhaus-date'
    },
    // Easter eggs
    bauhaus: {
      output: [
        'BAUHAUS DESIGN PRINCIPLES:',
        '1. FORM FOLLOWS FUNCTION',
        '2. SIMPLICITY IS BEAUTY', 
        '3. GEOMETRIC SHAPES',
        '4. PRIMARY COLORS',
        '5. BOLD TYPOGRAPHY'
      ],
      className: 'bauhaus-principles'
    },
    matrix: {
      output: [
        'WAKE UP, NEO...',
        'THE MATRIX HAS YOU...',
        'FOLLOW THE WHITE RABBIT.',
        'KNOCK KNOCK, NEO.'
      ],
      className: 'bauhaus-matrix'
    }
  };

  const executeCommand = (cmd) => {
    const command = cmd.toLowerCase().trim();
    const commandData = commands[command];

    if (commandData) {
      if (commandData.action === 'clear') {
        setHistory([
          { type: 'output', content: 'BAUHAUS TERMINAL v1.0', className: 'bauhaus-welcome' },
          { type: 'output', content: 'TYPE "HELP" FOR AVAILABLE COMMANDS', className: 'bauhaus-info' },
          { type: 'prompt', content: '', className: '' }
        ]);
      } else {
        const newEntries = [
          { type: 'input', content: `${currentPath}$ ${cmd}`, className: 'bauhaus-input' },
          ...commandData.output.map(line => ({ 
            type: 'output', 
            content: line, 
            className: commandData.className 
          })),
          { type: 'prompt', content: '', className: '' }
        ];
        setHistory(prev => [...prev.slice(0, -1), ...newEntries]);
      }
    } else {
      const newEntries = [
        { type: 'input', content: `${currentPath}$ ${cmd}`, className: 'bauhaus-input' },
        { type: 'output', content: `COMMAND NOT FOUND: ${cmd.toUpperCase()}`, className: 'bauhaus-error' },
        { type: 'output', content: 'TYPE "HELP" FOR AVAILABLE COMMANDS', className: 'bauhaus-info' },
        { type: 'prompt', content: '', className: '' }
      ];
      setHistory(prev => [...prev.slice(0, -1), ...newEntries]);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (input.trim()) {
      executeCommand(input);
      setInput('');
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Tab') {
      e.preventDefault();
      // Auto-complete functionality
      const availableCommands = Object.keys(commands);
      const matches = availableCommands.filter(cmd => 
        cmd.startsWith(input.toLowerCase())
      );
      if (matches.length === 1) {
        setInput(matches[0].toUpperCase());
      }
    }
  };

  // Auto-focus and scroll to bottom
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [history]);

  return (
    <div className="bauhaus-terminal-container">
      {/* Bauhaus Terminal Header */}
      <div className="bauhaus-terminal-header">
        <div className="bauhaus-terminal-controls">
          <div className="bauhaus-control bauhaus-control-red"></div>
          <div className="bauhaus-control bauhaus-control-yellow"></div>
          <div className="bauhaus-control bauhaus-control-blue"></div>
        </div>
        <div className="bauhaus-terminal-title">BAUHAUS TERMINAL</div>
        <div className="bauhaus-terminal-status">ACTIVE</div>
      </div>

      {/* Terminal Content */}
      <div 
        ref={terminalRef}
        className="bauhaus-terminal-content"
        onClick={() => inputRef.current?.focus()}
      >
        {history.map((line, index) => (
          <div key={index} className={`bauhaus-terminal-line ${line.className}`}>
            {line.type === 'prompt' ? (
              <form onSubmit={handleSubmit} className="bauhaus-terminal-prompt">
                <span className="bauhaus-prompt-path">{currentPath}$</span>
                <input
                  ref={inputRef}
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="bauhaus-terminal-input"
                  autoComplete="off"
                  spellCheck="false"
                />
                <span className="bauhaus-cursor">█</span>
              </form>
            ) : (
              <div className="bauhaus-terminal-output">
                {line.content}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Bauhaus Terminal Styles */}
      <style jsx>{`
        .bauhaus-terminal-container {
          font-family: 'Inter', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
          background: rgba(255, 255, 255, 0.03);
          backdrop-filter: blur(12px);
          border: 2px solid rgba(0, 0, 0, 0.8);
          border-radius: 0;
          box-shadow: 
            0 0 0 1px rgba(255, 255, 255, 0.1),
            0 8px 32px rgba(0, 0, 0, 0.3);
          overflow: hidden;
          max-width: 100%;
          height: 400px;
          display: flex;
          flex-direction: column;
        }

        /* Header */
        .bauhaus-terminal-header {
          background: rgba(0, 0, 0, 0.9);
          border-bottom: 2px solid rgba(227, 30, 36, 0.8);
          padding: 8px 16px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          min-height: 40px;
        }

        .bauhaus-terminal-controls {
          display: flex;
          gap: 6px;
        }

        .bauhaus-control {
          width: 12px;
          height: 12px;
          border: 1px solid rgba(0, 0, 0, 0.8);
        }

        .bauhaus-control-red { background: #E31E24; }
        .bauhaus-control-yellow { background: #FFD100; }
        .bauhaus-control-blue { background: #004CFF; }

        .bauhaus-terminal-title {
          color: #FFFFFF;
          font-weight: 700;
          font-size: 12px;
          letter-spacing: 0.1em;
          text-transform: uppercase;
        }

        .bauhaus-terminal-status {
          color: #00FF00;
          font-weight: 600;
          font-size: 10px;
          letter-spacing: 0.1em;
          text-transform: uppercase;
        }

        /* Content */
        .bauhaus-terminal-content {
          flex: 1;
          padding: 16px;
          overflow-y: auto;
          background: rgba(0, 0, 0, 0.05);
          cursor: text;
        }

        .bauhaus-terminal-line {
          margin-bottom: 4px;
          line-height: 1.4;
          font-size: 13px;
          font-weight: 500;
        }

        /* Prompt */
        .bauhaus-terminal-prompt {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .bauhaus-prompt-path {
          color: #E31E24;
          font-weight: 700;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }

        .bauhaus-terminal-input {
          background: transparent;
          border: none;
          outline: none;
          color: #FFFFFF;
          font-family: inherit;
          font-size: inherit;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.05em;
          flex: 1;
        }

        .bauhaus-cursor {
          color: #FFD100;
          animation: blink 1s infinite;
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }

        /* Output Styles */
        .bauhaus-terminal-output {
          font-weight: 500;
          letter-spacing: 0.02em;
        }

        /* Command-specific styling */
        .bauhaus-welcome {
          color: #E31E24;
          font-weight: 800;
          font-size: 14px;
          text-shadow: 0 0 8px rgba(227, 30, 36, 0.3);
        }

        .bauhaus-info {
          color: #004CFF;
          font-weight: 600;
        }

        .bauhaus-input {
          color: rgba(255, 255, 255, 0.9);
          font-weight: 600;
        }

        .bauhaus-error {
          color: #E31E24;
          font-weight: 700;
        }

        .bauhaus-help {
          color: #FFD100;
          font-weight: 600;
        }

        .bauhaus-about {
          color: #004CFF;
          font-weight: 600;
        }

        .bauhaus-skills {
          color: #00FF7F;
          font-weight: 600;
        }

        .bauhaus-projects {
          color: #FF6B35;
          font-weight: 600;
        }

        .bauhaus-contact {
          color: #9D4EDD;
          font-weight: 600;
        }

        .bauhaus-date {
          color: #FFFFFF;
          font-weight: 700;
        }

        .bauhaus-principles {
          color: #FFD100;
          font-weight: 700;
        }

        .bauhaus-matrix {
          color: #00FF00;
          font-weight: 700;
          text-shadow: 0 0 8px rgba(0, 255, 0, 0.5);
        }

        /* Scrollbar */
        .bauhaus-terminal-content::-webkit-scrollbar {
          width: 6px;
        }

        .bauhaus-terminal-content::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.2);
        }

        .bauhaus-terminal-content::-webkit-scrollbar-thumb {
          background: #E31E24;
          border-radius: 0;
        }

        .bauhaus-terminal-content::-webkit-scrollbar-thumb:hover {
          background: #FFD100;
        }

        /* Responsive */
        @media (max-width: 768px) {
          .bauhaus-terminal-container {
            height: 300px;
          }
          
          .bauhaus-terminal-content {
            padding: 12px;
          }
          
          .bauhaus-terminal-line {
            font-size: 12px;
          }
          
          .bauhaus-terminal-title {
            font-size: 10px;
          }
        }

        /* Selection */
        .bauhaus-terminal-content ::selection {
          background: rgba(227, 30, 36, 0.3);
          color: #FFFFFF;
        }

        /* Focus states */
        .bauhaus-terminal-input:focus {
          outline: 1px solid #FFD100;
          outline-offset: 2px;
        }
      `}</style>
    </div>
  );
};

export default Terminal;