import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

const ForceGraph = ({ data, width = 800, height = 600, compact = false }) => {
  const svgRef = useRef();

  useEffect(() => {
    if (!data || !data.nodes || !data.links) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove(); // Clear previous content

    const actualWidth = compact ? Math.min(width, 600) : width;
    const actualHeight = compact ? Math.min(height, 400) : height;

    svg.attr("width", actualWidth).attr("height", actualHeight);

    // Create a copy of the data to avoid mutating the original
    const nodes = data.nodes.map(d => ({ ...d }));
    const links = data.links.map(d => ({ ...d }));

    // BAUHAUS AUTHENTIC COLOR PALETTE
    const getNodeColor = (group) => {
      const bauhaus = {
        0: "#E31E24", // Alexandre - <PERSON>uhaus Red (primary)
        1: "#004CFF", // Programador - Bauhaus Blue (primary) 
        2: "#FFD100", // Designer - Bauhaus Yellow (primary)
        3: "#000000", // Audiovisual - Bauhaus Black
        4: "#FFFFFF", // Visão - Bauhaus White
      };
      return bauhaus[group] || "#808080"; // Bauhaus Gray fallback
    };

    // Complementary lighter colors for gradients (Bauhaus style)
    const getNodeGradientColor = (group) => {
      const bauhausGradients = {
        0: "#FF6B6B", // Light red gradient
        1: "#4D7FFF", // Light blue gradient  
        2: "#FFE066", // Light yellow gradient
        3: "#333333", // Dark gray gradient
        4: "#F5F5F5", // Off-white gradient
      };
      return bauhausGradients[group] || "#A0A0A0";
    };

    // Get text color for contrast (Bauhaus principle)
    const getTextColor = (group) => {
      const textColors = {
        0: "#FFFFFF", // White text on red
        1: "#FFFFFF", // White text on blue
        2: "#000000", // Black text on yellow (Bauhaus contrast)
        3: "#FFFFFF", // White text on black
        4: "#000000", // Black text on white
      };
      return textColors[group] || "#FFFFFF";
    };

    // Create Bauhaus-style gradients
    const defs = svg.append("defs");
    
    [0, 1, 2, 3, 4].forEach(group => {
      const gradient = defs.append("radialGradient")
        .attr("id", `bauhausGradient${group}`)
        .attr("cx", "35%")
        .attr("cy", "35%");
      
      if (group === 4) {
        // Special handling for white node - reverse gradient
        gradient.append("stop")
          .attr("offset", "0%")
          .attr("stop-color", "#FFFFFF")
          .attr("stop-opacity", 1);
        
        gradient.append("stop")
          .attr("offset", "100%")
          .attr("stop-color", "#E8E8E8")
          .attr("stop-opacity", 1);
      } else {
        gradient.append("stop")
          .attr("offset", "0%")
          .attr("stop-color", getNodeGradientColor(group))
          .attr("stop-opacity", 0.9);
        
        gradient.append("stop")
          .attr("offset", "100%")
          .attr("stop-color", getNodeColor(group))
          .attr("stop-opacity", 1);
      }
    });

    // Create the simulation with refined forces
    const simulation = d3.forceSimulation(nodes)
      .force("link", d3.forceLink(links).id(d => d.id).distance(compact ? 90 : 140))
      .force("charge", d3.forceManyBody().strength(compact ? -250 : -450))
      .force("center", d3.forceCenter(actualWidth / 2, actualHeight / 2))
      .force("collision", d3.forceCollide().radius(d => (d.size || 20) + 8));

    // Create Bauhaus-style links - clean and geometric
    const link = svg.append("g")
      .attr("class", "links")
      .selectAll("line")
      .data(links)
      .join("line")
      .attr("stroke", "#000000") // Bauhaus black for structure
      .attr("stroke-opacity", 0.3)
      .attr("stroke-width", 1.2) // Slightly thicker for Bauhaus boldness
      .style("transition", "all 0.2s ease");

    // Create node groups for better organization
    const nodeGroup = svg.append("g")
      .attr("class", "nodes")
      .selectAll("g")
      .data(nodes)
      .join("g")
      .attr("class", "node-group")
      .style("cursor", "pointer");

    // Create Bauhaus geometric nodes
    const nodeCircles = nodeGroup.append("circle")
      .attr("class", "node-circle")
      .attr("r", d => {
        // Larger circles to fit text better - Bauhaus functional design
        const baseSize = d.size || 20;
        const textLength = (d.id || '').length;
        return Math.max(32, baseSize + textLength * 2 + 10);
      })
      .attr("fill", d => `url(#bauhausGradient${d.group})`)
      .attr("stroke", d => {
        // Bauhaus contrast borders
        return d.group === 4 ? "#000000" : "#FFFFFF"; // Black border on white, white on colors
      })
      .attr("stroke-width", 2.5) // Bold Bauhaus borders
      .style("filter", "drop-shadow(0 3px 8px rgba(0,0,0,0.15))")
      .style("transition", "all 0.2s ease");

    // Add Bauhaus typography - bold and functional
    const nodeLabels = nodeGroup.append("text")
      .attr("class", "node-label")
      .text(d => d.id)
      .attr("text-anchor", "middle")
      .attr("dominant-baseline", "central")
      .attr("fill", d => getTextColor(d.group))
      .attr("font-family", "'Inter', -apple-system, BlinkMacSystemFont, sans-serif")
      .attr("font-size", d => {
        const radius = Math.max(32, (d.size || 20) + (d.id || '').length * 2 + 10);
        return Math.max(11, Math.min(15, radius / 2.8));
      })
      .attr("font-weight", d => d.group === 0 ? "700" : "600") // Bold Bauhaus typography
      .style("pointer-events", "none")
      .style("user-select", "none")
      .style("text-shadow", d => {
        // Text shadow for better contrast - Bauhaus functional approach
        return d.group === 2 ? "0 1px 2px rgba(0,0,0,0.5)" : 
               d.group === 4 ? "0 1px 2px rgba(0,0,0,0.3)" : 
               "0 1px 3px rgba(0,0,0,0.6)";
      });

    // Enhanced Bauhaus hover effects - geometric and bold
    nodeGroup
      .on("mouseover", function(event, d) {
        // Scale up with Bauhaus precision
        d3.select(this).select(".node-circle")
          .transition()
          .duration(150) // Snappy Bauhaus timing
          .attr("r", d => {
            const baseSize = d.size || 20;
            const textLength = (d.id || '').length;
            return (Math.max(32, baseSize + textLength * 2 + 10)) * 1.2;
          })
          .attr("stroke-width", 4) // Bold Bauhaus emphasis
          .style("filter", "drop-shadow(0 6px 16px rgba(0,0,0,0.25))");

        // Bold text emphasis
        d3.select(this).select(".node-label")
          .transition()
          .duration(150)
          .attr("font-weight", "700");

        // Highlight connected links with Bauhaus style
        link
          .transition()
          .duration(150)
          .attr("stroke-opacity", l => (l.source === d || l.target === d) ? 0.8 : 0.1)
          .attr("stroke", l => (l.source === d || l.target === d) ? "#E31E24" : "#000000") // Bauhaus red for active
          .attr("stroke-width", l => (l.source === d || l.target === d) ? 2.5 : 1.2);

        // Geometric highlighting of connected nodes
        nodeGroup.selectAll(".node-circle")
          .transition()
          .duration(150)
          .style("opacity", n => {
            if (n === d) return 1;
            return links.some(l => 
              (l.source === d && l.target === n) || 
              (l.target === d && l.source === n)
            ) ? 1 : 0.4;
          });

        nodeGroup.selectAll(".node-label")
          .transition()
          .duration(150)
          .style("opacity", n => {
            if (n === d) return 1;
            return links.some(l => 
              (l.source === d && l.target === n) || 
              (l.target === d && l.source === n)
            ) ? 1 : 0.4;
          });
      })
      .on("mouseout", function(event, d) {
        // Reset with Bauhaus precision
        d3.select(this).select(".node-circle")
          .transition()
          .duration(150)
          .attr("r", d => {
            const baseSize = d.size || 20;
            const textLength = (d.id || '').length;
            return Math.max(32, baseSize + textLength * 2 + 10);
          })
          .attr("stroke-width", 2.5)
          .style("filter", "drop-shadow(0 3px 8px rgba(0,0,0,0.15))");

        // Reset text
        d3.select(this).select(".node-label")
          .transition()
          .duration(150)
          .attr("font-weight", d => d.group === 0 ? "700" : "600");

        // Reset all links to Bauhaus black
        link
          .transition()
          .duration(150)
          .attr("stroke-opacity", 0.3)
          .attr("stroke", "#000000")
          .attr("stroke-width", 1.2);

        // Reset all nodes
        nodeGroup.selectAll(".node-circle")
          .transition()
          .duration(150)
          .style("opacity", 1);

        nodeGroup.selectAll(".node-label")
          .transition()
          .duration(150)
          .style("opacity", 1);
      });

    // Bauhaus tooltips with clean information
    nodeGroup.append("title")
      .text(d => `${d.id}\nÁrea: ${getGroupName(d.group)}\nArraste para reorganizar`);

    // Helper function to get Bauhaus group names
    function getGroupName(group) {
      const names = {
        0: "Profissional Core",
        1: "Desenvolvimento",
        2: "Design Visual", 
        3: "Produção",
        4: "Estratégia"
      };
      return names[group] || "Outros";
    }

    // Enhanced drag behavior with Bauhaus visual feedback
    const drag = d3.drag()
      .on("start", function(event, d) {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
        
        // Strong Bauhaus visual feedback
        d3.select(this).select(".node-circle")
          .attr("stroke-width", 5)
          .style("filter", "drop-shadow(0 8px 20px rgba(0,0,0,0.3))");
      })
      .on("drag", function(event, d) {
        d.fx = event.x;
        d.fy = event.y;
      })
      .on("end", function(event, d) {
        if (!event.active) simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
        
        // Reset to Bauhaus standard
        d3.select(this).select(".node-circle")
          .attr("stroke-width", 2.5)
          .style("filter", "drop-shadow(0 3px 8px rgba(0,0,0,0.15))");
      });

    nodeGroup.call(drag);

    // Update positions on simulation tick
    simulation.on("tick", () => {
      link
        .attr("x1", d => d.source.x)
        .attr("y1", d => d.source.y)
        .attr("x2", d => d.target.x)
        .attr("y2", d => d.target.y);

      nodeGroup
        .attr("transform", d => `translate(${d.x},${d.y})`);
    });

    // Cleanup function
    return () => {
      simulation.stop();
    };
  }, [data, width, height, compact]);

  // Bauhaus legend data with authentic colors
  const bauhausLegendData = [
    { group: 0, name: "Profissional", color: "#E31E24" },
    { group: 1, name: "Desenvolvimento", color: "#004CFF" },
    { group: 2, name: "Design", color: "#FFD100" },
    { group: 3, name: "Produção", color: "#000000" },
    { group: 4, name: "Estratégia", color: "#FFFFFF", border: "#000000" }
  ];

  return (
    <div className="flex flex-col items-center font-['Inter',sans-serif]">
      {/* Bauhaus Header - Clean and Geometric */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-black mb-3 tracking-tight font-['Inter',sans-serif] uppercase">
          NETWORK GRAPH
        </h2>
        <div className="flex justify-center space-x-2">
          <div className="w-8 h-1 bg-red-600"></div>
          <div className="w-8 h-1 bg-blue-600"></div>
          <div className="w-8 h-1 bg-yellow-400"></div>
        </div>
      </div>

      {/* Graph Container with Bauhaus styling */}
      <div className="bg-gray-50 border-4 border-black rounded-none p-8 shadow-lg">
        <div className="bg-white border-2 border-gray-300 p-6">
          <svg 
            ref={svgRef} 
            className="bg-white"
            style={{ 
              fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif"
            }}
          />
        </div>
      </div>

      {/* Bauhaus Legend - Geometric and Functional */}
      <div className="mt-8 grid grid-cols-2 md:grid-cols-5 gap-6 max-w-4xl">
        {bauhausLegendData.map((item) => (
          <div key={item.group} className="flex items-center space-x-3">
            <div 
              className="w-6 h-6 border-2"
              style={{ 
                backgroundColor: item.color,
                borderColor: item.border || (item.color === "#FFFFFF" ? "#000000" : item.color)
              }}
            />
            <span className="text-sm font-bold text-black font-['Inter',sans-serif] uppercase tracking-wide">
              {item.name}
            </span>
          </div>
        ))}
      </div>

      {/* Bauhaus Instructions */}
      <div className="mt-8 max-w-md text-center">
        <p className="text-sm font-medium text-black font-['Inter',sans-serif] leading-relaxed uppercase tracking-wide">
          ARRASTE • INTERAJA • EXPLORE
        </p>
      </div>
    </div>
  );
};

export default ForceGraph;