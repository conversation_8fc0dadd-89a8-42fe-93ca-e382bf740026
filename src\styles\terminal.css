/* Terminal.css - Bauhaus Terminal Styling */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

/* Bauhaus Terminal Variables */
:root {
  --bauhaus-red: #E31E24;
  --bauhaus-blue: #004CFF;
  --bauhaus-yellow: #FFD100;
  --bauhaus-black: #000000;
  --bauhaus-white: #FFFFFF;
  --bauhaus-green: #00FF7F;
  --bauhaus-purple: #9D4EDD;
  --bauhaus-orange: #FF6B35;
}

/* Terminal Container - Glassmorphism with Bauhaus */
.bauhaus-terminal-wrapper {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.03) 100%);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 2px solid rgba(227, 30, 36, 0.3);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

/* Enhanced Glassmorphism Background */
.bauhaus-terminal-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(227, 30, 36, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 76, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(255, 209, 0, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Terminal Font Stack - Monospace + Inter */
.bauhaus-terminal-container,
.bauhaus-terminal-container * {
  font-family: 'JetBrains Mono', 'Inter', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important;
}

/* Header Enhanced Styling */
.bauhaus-terminal-header {
  background: linear-gradient(90deg, 
    rgba(0, 0, 0, 0.95) 0%, 
    rgba(227, 30, 36, 0.1) 50%, 
    rgba(0, 0, 0, 0.95) 100%);
  border-bottom: 2px solid var(--bauhaus-red);
  backdrop-filter: blur(8px);
  position: relative;
}

.bauhaus-terminal-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--bauhaus-yellow) 50%, 
    transparent 100%);
}

/* Enhanced Controls */
.bauhaus-control {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 1px 2px rgba(0, 0, 0, 0.3);
}

.bauhaus-control:hover {
  transform: scale(1.1);
  box-shadow: 
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    0 2px 4px rgba(0, 0, 0, 0.4);
}

.bauhaus-control-red:hover {
  box-shadow: 0 0 8px rgba(227, 30, 36, 0.6);
}

.bauhaus-control-yellow:hover {
  box-shadow: 0 0 8px rgba(255, 209, 0, 0.6);
}

.bauhaus-control-blue:hover {
  box-shadow: 0 0 8px rgba(0, 76, 255, 0.6);
}

/* Terminal Title with Animation */
.bauhaus-terminal-title {
  position: relative;
  background: linear-gradient(90deg, 
    var(--bauhaus-white) 0%, 
    var(--bauhaus-yellow) 50%, 
    var(--bauhaus-white) 100%);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: terminalTitleFlow 3s ease-in-out infinite;
}

@keyframes terminalTitleFlow {
  0%, 100% { background-position: 0% 0%; }
  50% { background-position: 100% 0%; }
}

/* Status Indicator */
.bauhaus-terminal-status {
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
}

.bauhaus-terminal-status::before {
  content: '';
  width: 8px;
  height: 8px;
  background: var(--bauhaus-green);
  border-radius: 50%;
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* Content Area Enhancement */
.bauhaus-terminal-content {
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.02) 0%,
    rgba(0, 0, 0, 0.08) 100%);
  position: relative;
}

/* Scanline Effect */
.bauhaus-terminal-content::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(0, 255, 127, 0.6) 50%, 
    transparent 100%);
  animation: scanline 3s linear infinite;
  pointer-events: none;
}

@keyframes scanline {
  0% { transform: translateY(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(400px); opacity: 0; }
}

/* Enhanced Prompt */
.bauhaus-prompt-path {
  position: relative;
  text-shadow: 0 0 4px rgba(227, 30, 36, 0.5);
}

.bauhaus-prompt-path::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--bauhaus-red);
  opacity: 0.7;
}

/* Enhanced Input */
.bauhaus-terminal-input {
  position: relative;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.02) 0%, 
    rgba(255, 255, 255, 0.05) 50%, 
    rgba(255, 255, 255, 0.02) 100%);
  border-bottom: 1px solid rgba(255, 209, 0, 0.3);
  padding: 2px 4px;
  transition: all 0.2s ease;
}

.bauhaus-terminal-input:focus {
  background: rgba(255, 209, 0, 0.05);
  border-bottom-color: var(--bauhaus-yellow);
  box-shadow: 0 0 8px rgba(255, 209, 0, 0.2);
}

/* Enhanced Cursor */
.bauhaus-cursor {
  background: linear-gradient(45deg, 
    var(--bauhaus-yellow) 0%, 
    var(--bauhaus-red) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 8px rgba(255, 209, 0, 0.8);
}

/* Command Output Enhancements */
.bauhaus-welcome {
  text-shadow: 
    0 0 4px rgba(227, 30, 36, 0.5),
    0 0 8px rgba(227, 30, 36, 0.3);
  position: relative;
}

.bauhaus-help {
  background: linear-gradient(45deg, 
    var(--bauhaus-yellow) 0%, 
    rgba(255, 209, 0, 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bauhaus-error {
  text-shadow: 0 0 6px rgba(227, 30, 36, 0.7);
  animation: errorFlash 0.5s ease-in-out;
}

@keyframes errorFlash {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Matrix Effect for Easter Egg */
.bauhaus-matrix {
  text-shadow: 
    0 0 4px var(--bauhaus-green),
    0 0 8px var(--bauhaus-green),
    0 0 12px var(--bauhaus-green);
  animation: matrixFlicker 2s ease-in-out infinite;
}

@keyframes matrixFlicker {
  0%, 100% { opacity: 1; }
  25%, 75% { opacity: 0.8; }
  50% { opacity: 0.9; }
}

/* Enhanced Scrollbar */
.bauhaus-terminal-content::-webkit-scrollbar {
  width: 8px;
}

.bauhaus-terminal-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-left: 1px solid rgba(227, 30, 36, 0.2);
}

.bauhaus-terminal-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, 
    var(--bauhaus-red) 0%, 
    var(--bauhaus-yellow) 100%);
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.bauhaus-terminal-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, 
    var(--bauhaus-yellow) 0%, 
    var(--bauhaus-blue) 100%);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .bauhaus-terminal-wrapper {
    border-width: 1px;
    backdrop-filter: blur(12px);
  }
  
  .bauhaus-terminal-header {
    padding: 6px 12px;
  }
  
  .bauhaus-control {
    width: 10px;
    height: 10px;
  }
  
  .bauhaus-terminal-title {
    font-size: 10px;
  }
  
  .bauhaus-terminal-content::after {
    height: 1px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .bauhaus-terminal-wrapper {
    border-width: 3px;
    backdrop-filter: blur(20px);
  }
  
  .bauhaus-terminal-input {
    border-bottom-width: 2px;
  }
  
  .bauhaus-prompt-path::after {
    height: 2px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .bauhaus-terminal-title,
  .bauhaus-terminal-status::before,
  .bauhaus-terminal-content::after,
  .bauhaus-cursor,
  .bauhaus-matrix {
    animation: none;
  }
  
  .bauhaus-control:hover {
    transform: none;
  }
}