/* Import Inter font - Bauhaus functional typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');

/* BAUHAUS DESIGN SYSTEM */
:root {
  /* Authentic Bauhaus Colors */
  --bauhaus-red: #E31E24;
  --bauhaus-blue: #004CFF;
  --bauhaus-yellow: #FFD100;
  --bauhaus-black: #000000;
  --bauhaus-white: #FFFFFF;
  --bauhaus-gray: #808080;
  
  /* Bauhaus Typography */
  --bauhaus-font: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Bauhaus Container - Geometric and Functional */
.force-graph-container {
  font-family: var(--bauhaus-font);
  background: var(--bauhaus-white);
  border: 4px solid var(--bauhaus-black);
  box-shadow: 8px 8px 0 var(--bauhaus-black);
}

/* SVG Bauhaus Styling */
.force-graph-container svg {
  font-family: var(--bauhaus-font);
  background: var(--bauhaus-white);
  border: 2px solid var(--bauhaus-gray);
  font-weight: 600;
  text-rendering: optimizeLegibility;
}

/* Bauhaus Node Circles - Bold and Geometric */
.force-graph-container .node-circle {
  transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  stroke-linejoin: miter;
  stroke-linecap: square;
}

.force-graph-container .node-circle:hover {
  filter: drop-shadow(6px 6px 0 rgba(0, 0, 0, 0.3)) !important;
  transform: translate(-2px, -2px);
}

/* Bauhaus Links - Structural and Clean */
.force-graph-container .links line {
  transition: all 0.15s ease;
  stroke-linecap: square;
  stroke-linejoin: miter;
}

.force-graph-container .links line:hover {
  stroke: var(--bauhaus-red) !important;
  stroke-width: 3px !important;
}

/* Bauhaus Typography - Bold and Functional */
.force-graph-container .node-label {
  font-family: var(--bauhaus-font);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  text-anchor: middle;
  dominant-baseline: central;
  transition: all 0.15s ease;
}

/* Bauhaus Header Styling */
.bauhaus-header {
  font-family: var(--bauhaus-font);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 800;
  color: var(--bauhaus-black);
  margin-bottom: 32px;
}

.bauhaus-title {
  font-size: 36px;
  line-height: 1.1;
  margin-bottom: 16px;
  text-shadow: 3px 3px 0 var(--bauhaus-gray);
}

/* Bauhaus Color Bars */
.bauhaus-bars {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 12px;
}

.bauhaus-bar {
  width: 48px;
  height: 6px;
  border: 2px solid var(--bauhaus-black);
}

.bauhaus-bar--red { background: var(--bauhaus-red); }
.bauhaus-bar--blue { background: var(--bauhaus-blue); }
.bauhaus-bar--yellow { background: var(--bauhaus-yellow); }

/* Bauhaus Container Styling */
.bauhaus-graph-wrapper {
  background: #F5F5F5;
  border: 6px solid var(--bauhaus-black);
  padding: 32px;
  position: relative;
  box-shadow: 12px 12px 0 var(--bauhaus-black);
}

.bauhaus-graph-wrapper::before {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  background: linear-gradient(45deg, 
    var(--bauhaus-red) 0%, 
    var(--bauhaus-red) 25%, 
    var(--bauhaus-blue) 25%, 
    var(--bauhaus-blue) 50%, 
    var(--bauhaus-yellow) 50%, 
    var(--bauhaus-yellow) 75%, 
    var(--bauhaus-black) 75%);
  z-index: -1;
  filter: blur(0px);
}

.bauhaus-graph-inner {
  background: var(--bauhaus-white);
  border: 3px solid var(--bauhaus-gray);
  padding: 24px;
  position: relative;
}

/* Bauhaus Legend - Geometric Grid */
.bauhaus-legend {
  font-family: var(--bauhaus-font);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
  margin-top: 32px;
  max-width: 800px;
}

.bauhaus-legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: var(--bauhaus-white);
  border: 2px solid var(--bauhaus-black);
  transition: all 0.15s ease;
}

.bauhaus-legend-item:hover {
  background: var(--bauhaus-yellow);
  transform: translate(-2px, -2px);
  box-shadow: 4px 4px 0 var(--bauhaus-black);
}

.bauhaus-legend-dot {
  width: 24px;
  height: 24px;
  border: 2px solid var(--bauhaus-black);
  flex-shrink: 0;
}

.bauhaus-legend-dot--white {
  background: var(--bauhaus-white);
  border-color: var(--bauhaus-black);
}

.bauhaus-legend-text {
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: var(--bauhaus-black);
  line-height: 1.2;
}

/* Bauhaus Instructions - Functional Typography */
.bauhaus-instructions {
  font-family: var(--bauhaus-font);
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.15em;
  color: var(--bauhaus-black);
  text-align: center;
  margin-top: 24px;
  padding: 16px;
  background: var(--bauhaus-white);
  border: 3px solid var(--bauhaus-black);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Bauhaus Geometric Elements */
.bauhaus-geometric-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

.bauhaus-geometric-bg::before {
  content: '';
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: var(--bauhaus-red);
  border: 2px solid var(--bauhaus-black);
  transform: rotate(45deg);
}

.bauhaus-geometric-bg::after {
  content: '';
  position: absolute;
  bottom: 20px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 35px solid var(--bauhaus-blue);
  border: 2px solid var(--bauhaus-black);
}

/* Responsive Bauhaus Design */
@media (max-width: 768px) {
  .bauhaus-title {
    font-size: 28px;
  }
  
  .bauhaus-graph-wrapper {
    border-width: 4px;
    padding: 16px;
    box-shadow: 8px 8px 0 var(--bauhaus-black);
  }
  
  .bauhaus-graph-inner {
    padding: 16px;
  }
  
  .bauhaus-legend {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
  
  .bauhaus-legend-text {
    font-size: 11px;
  }
  
  .bauhaus-instructions {
    font-size: 12px;
    padding: 12px;
  }
}

/* Animation for Bauhaus Elements */
@keyframes bauhausSlideIn {
  from {
    transform: translate(20px, 20px);
    opacity: 0;
  }
  to {
    transform: translate(0, 0);
    opacity: 1;
  }
}

.force-graph-container {
  animation: bauhausSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Bauhaus Focus States */
.force-graph-container:focus-within {
  outline: 4px solid var(--bauhaus-red);
  outline-offset: 4px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --bauhaus-red: #FF0000;
    --bauhaus-blue: #0000FF;
    --bauhaus-yellow: #FFFF00;
    --bauhaus-black: #000000;
    --bauhaus-white: #FFFFFF;
  }
  
  .bauhaus-graph-wrapper {
    border-width: 8px;
  }
  
  .force-graph-container .node-circle {
    stroke-width: 4px !important;
  }
}